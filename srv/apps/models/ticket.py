from functools import cached_property

from tortoise import Model, fields

THEMES = {
    '数字展览': {'探秘海昏侯国', '熊猫世界', '宇宙猜想'},
    '空间剧场': {'宇宙奇旅', '奇幻森林', '恐龙王国'},
}
TIMESLOTS = ['平日票', '全通票', '早鸟票', '特惠票']
GRADES = ['单人', '双人', '三人', '儿童', '亲子', '家庭']


class ExhiHall(Model):
    """展馆"""

    id = fields.IntField(pk=True, description='展馆ID')
    name = fields.CharField(max_length=64, description='展馆名称')
    city = fields.CharField(max_length=16, description='所在城市')
    addr = fields.CharField(max_length=128, description='详细地址')
    notice = fields.CharField(max_length=256, description='入馆公告')


class Category(Model):
    """展票类型"""

    id = fields.IntField(pk=True)
    timeslot = fields.CharField(max_length=16, description='场次')
    grade = fields.CharField(max_length=16, description='票档')
    original = fields.FloatField(description='原价')
    price = fields.FloatField(description='价格')


class Ticket(Model):
    """展票"""

    id = fields.IntField(pk=True)
    title = fields.CharField(max_length=256)
    type = fields.CharField(max_length=16, description='展票类型')
    themes: list[str] = fields.JSONField(default=[], description='主题列表')  # type: ignore

    # 展馆信息及票价
    eid = fields.IntField(null=False, db_index=True, description='展馆ID')
    cids: list[int] = fields.JSONField(default=[], description='票型ID列表')  # type: ignore

    # 展票图片素材 URL
    thumbnail = fields.CharField(max_length=256, description='缩略图 URL')
    banner = fields.CharField(max_length=256, description='横幅图 URL')
    detail: list[str] = fields.JSONField(default=[], description='详情图 URL 列表')  # type: ignore

    @cached_property
    async def exhihall(self) -> ExhiHall:
        """展馆"""
        return await ExhiHall.get(id=self.eid)

    @cached_property
    async def categories(self) -> list[Category]:
        """展票类型列表"""
        return await Category.filter(id__in=self.cids)

    async def save(self, *args, **kwargs):
        """保存前处理"""
        # 1. 检查主题是否存在
        if self.type not in THEMES or not THEMES[self.type].issuperset(self.themes):
            raise ValueError('主题不存在')
        # 2. 检查展馆是否存在
        # 3. 检查票型是否存在

        return await super().save(*args, **kwargs)

    async def prices(self):
        """展票价格列表"""
        categories = await self.categories
        result = {}
        for catg in categories:
            result[catg.timeslot]

    async def to_dict(self):
        exhihall = await self.exhihall
        return {
            'id': self.id,
            'title': self.title,
            'city': exhihall.city,
            'addr': exhihall.addr,
            'notice': exhihall.notice,
            'type': self.type,
            'themes': self.themes,
            'price': sorted(catg.price for catg in await self.categories),
            'thumbnail': self.thumbnail,
            'banner': self.banner,
            'detail': self.detail,
        }
