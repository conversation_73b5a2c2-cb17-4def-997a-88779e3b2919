from collections.abc import Iterable
from itertools import islice
from typing import Any, Self

from tortoise import Tortoise
from tortoise.backends.base.client import BaseDBAsyncClient
from tortoise.expressions import Q
from tortoise.fields import IntField
from tortoise.models import Model as TortoiseModel

from libs.cache import redis

FieldNames = Iterable[str]

EMPTY = object()
CACHE_MAX_AGE = 86400
DATABASE = {
    'connections': {'default': 'postgres://seamile:7758521@localhost:5432/huaxia'},
    'apps': {
        'huaxia': {
            'default_connection': 'default',
            'models': ['apps.models.ticket'],
        }
    },
}


def ibatch(iterable, batch_size):
    """分批迭代器"""
    it = iter(iterable)
    while batch := list(islice(it, batch_size)):
        yield batch


class Model(TortoiseModel):
    """Model 基类"""

    id = IntField(primary_key=True)

    class Meta:  # type: ignore
        abstract = True

    def __str__(self) -> str:
        return f'{self.__class__.__name__}({self.id})'

    def __repr__(self) -> str:
        return f'{self.__class__.__name__}(id={self.id})'

    def __format__(self, format_spec: str) -> str:
        return self.__str__().__format__(format_spec)

    @staticmethod
    async def init_db():
        await Tortoise.init(config=DATABASE)

    async def set_cache(self):
        """设置 Model 对象缓存"""
        key = f'{self.__class__.__name__}::{self.id}'
        await redis.set(key, self, CACHE_MAX_AGE)

    async def del_cache(self):
        """删除 Model 对象缓存"""
        key = f'{self.__class__.__name__}::{self.id}'
        return await redis.delete(key)

    @classmethod
    async def rebuild_cache(cls, *primary_keys: int):
        """重建 Model 对象缓存"""
        # 获取所有主键
        if not primary_keys:
            keys = await redis.keys(f'{cls.__name__}::*')
            primary_keys = tuple(int(key.split('::')[-1]) for key in keys)

        if not primary_keys:
            return

        for kilo_primary_key in ibatch(primary_keys, 1000):
            # 批量获取对象并保存
            objs = await cls.filter(id__in=kilo_primary_key)
            kilo_keys = {f'{cls.__name__}::{obj.id}': obj for obj in objs}
            await redis.mset(kilo_keys)
            # 重设过期时间
            pipeline = redis.pipeline()
            for key in kilo_keys:
                pipeline.expire(key, CACHE_MAX_AGE)
            await pipeline.execute()

    @classmethod
    async def clear_cache(cls):
        """清空 Model 对象缓存"""
        return await redis.del_pattern(f'{cls.__name__}::*')

    @classmethod
    def fields(cls):
        """获取 Model 对象的字段"""
        return cls._meta.fields.copy()

    @classmethod
    async def get(cls, *args: Q, using_db: BaseDBAsyncClient | None = None, **kwargs: Any) -> Self:  # type: ignore
        """获取 Model 对象"""
        pk = kwargs.get('pk') or kwargs.get('id')
        if pk is not None:
            key = f'{cls.__name__}::{pk}'
            if obj := await redis.get(key):
                await redis.expire(key, CACHE_MAX_AGE)  # 每次获取对象时重置过期时间
                return obj  # type: ignore

        obj = await super().get(*args, using_db=using_db, **kwargs)

        await cls.set_cache(obj)

        return obj

    async def save(self, *args, **kwargs) -> None:
        """保存 Model 对象"""
        await super().save(*args, **kwargs)
        await self.set_cache()

    async def delete(self, *args, **kwargs) -> None:
        """删除 Model 对象"""
        await super().delete(*args, **kwargs)
        await self.del_cache()

    def to_dict(
        self,
        only: FieldNames | None = None,
        exclude: FieldNames | None = None,
        extra: FieldNames | None = None,
    ) -> dict[str, Any]:
        keys = set(only or self._meta.fields)  # type: ignore
        if exclude is not None:
            keys -= set(exclude)
        if extra is not None:  # 追加字段, 常为 property 属性
            keys |= set(extra)

        attrs = {key: getattr(self, key) for key in keys}
        return attrs
