from collections.abc import Awaitable, Mapping
from pickle import HIGHEST_PROTOCOL, UnpicklingError, dumps, loads  # noqa: S403
from typing import Any

from redis.asyncio import Redis as AioRedis
from redis.typing import AbsExpiryT, AnyKeyT, EncodableT, ExpiryT, KeysT, KeyT, PatternT

REDIS = {'host': 'localhost', 'port': 6379, 'db': 5}


__all__ = ['Redis', 'redis']


class Redis(AioRedis):
    def set(
        self,
        name,
        value: Any,
        ex: ExpiryT | None = None,
        px: ExpiryT | None = None,
        nx: bool = False,
        xx: bool = False,
        keepttl: bool = False,
        get: bool = False,
        exat: AbsExpiryT | None = None,
        pxat: AbsExpiryT | None = None,
    ):
        """Set the value at key ``name`` to ``value``

        ``ex`` sets an expire flag on key ``name`` for ``ex`` seconds.
        ``px`` sets an expire flag on key ``name`` for ``px`` milliseconds.
        ``nx`` if set to True, set the value at key ``name`` to ``value`` only
            if it does not exist.
        ``xx`` if set to True, set the value at key ``name`` to ``value`` only
            if it already exists.
        ``keepttl`` if True, retain the time to live associated with the key.
            (Available since Redis 6.0)
        ``get`` if True, set the value at key ``name`` to ``value`` and return
            the old value stored at key, or None if the key did not exist.
            (Available since Redis 6.2)
        ``exat`` sets an expire flag on key ``name`` for ``ex`` seconds,
            specified in unix time.
        ``pxat`` sets an expire flag on key ``name`` for ``ex`` milliseconds,
            specified in unix time.
        """
        v_pickled = dumps(value, HIGHEST_PROTOCOL)  # 将需要保存的值序列化处理
        return super().set(name, v_pickled, ex, px, nx, xx, keepttl, get, exat, pxat)

    async def get(self, name, default=None):
        """Return the value at key ``name``, or ``default`` if the key doesn't exist"""
        value = await super().get(name)

        try:
            return default if value is None else loads(value)  # noqa: S301
        except UnpicklingError:
            return value

    def mset(self, mapping: Mapping[AnyKeyT, EncodableT]):  # type: ignore
        """
        Sets key/values based on a mapping. Mapping is a dictionary of
        key/value pairs. Both keys and values should be strings or types that
        can be cast to a string via str().
        """
        for k, v in mapping.items():
            mapping[k] = dumps(v, HIGHEST_PROTOCOL)  # type: ignore
        return super().mset(mapping)  # type: ignore

    async def mget(self, keys: KeysT, *args: EncodableT):
        """Returns a list of values ordered identically to ``keys``"""
        values = await super().mget(keys, *args)  # type: ignore
        for idx, value in enumerate(values):
            if value is not None:
                try:
                    values[idx] = loads(value)  # noqa: S301
                except UnpicklingError:
                    pass
        return values

    async def keys(self, pattern: PatternT = '*', **kwargs) -> list[str]:
        """Returns a list of keys matching ``pattern``"""
        result = [k.decode('utf-8') for k in await super().keys(pattern, **kwargs)]
        result.sort()
        return result

    def hset(  # type: ignore
        self,
        name: str,
        key: str | None = None,
        value: Any = None,
        mapping: dict | None = None,
        items: list | None = None,
    ) -> Awaitable[int]:
        """
        Set ``key`` to ``value`` within hash ``name``,
        ``mapping`` accepts a dict of key/value pairs that will be
        added to hash ``name``.
        ``items`` accepts a list of key/value pairs that will be
        added to hash ``name``.
        Returns the number of fields that were added.
        """
        if value is not None:
            value = dumps(value, HIGHEST_PROTOCOL)  # type: ignore
        if mapping is not None:
            mapping = {k: dumps(v, HIGHEST_PROTOCOL) for k, v in mapping.items()}  # type: ignore
        if items is not None:
            items = [v if i % 2 == 0 else dumps(v, HIGHEST_PROTOCOL) for i, v in enumerate(items)]  # type: ignore

        return super().hset(name, key, value, mapping, items)  # type: ignore

    async def hget(self, name, key, default=None):
        """Return the value of ``key`` within the hash ``name``"""
        value = await super().hget(name, key)  # type: ignore

        try:
            return default if value is None else loads(value)  # type: ignore  # noqa: S301
        except UnpicklingError:
            return value

    def hmset(self, name: KeyT, mapping: Mapping) -> Awaitable[str]:  # type: ignore
        """
        Set key to value within hash ``name`` for each corresponding
        key and value from the ``mapping`` dict.
        """
        for k, v in mapping.items():
            mapping[k] = dumps(v, HIGHEST_PROTOCOL)  # type: ignore
        return super().hmset(name, mapping)  # type: ignore

    async def hmget(self, name, keys, *args) -> list:
        """Returns a list of values ordered identically to ``keys``"""
        values_list = await super().hmget(name, keys, *args)  # type:ignore
        for idx, value in enumerate(values_list):
            if value is not None:
                try:
                    values_list[idx] = loads(value)  # noqa: S301
                except UnpicklingError:
                    pass
        return values_list

    async def hgetall(self, name: KeyT) -> dict[str, Any]:
        """Return a Python dict of the hash's name/value pairs"""
        items = {}
        for k, v in (await super().hgetall(name)).items():  # type: ignore
            k = k.decode('utf-8')
            try:
                items[k] = loads(v)  # noqa: S301
            except UnpicklingError:
                items[k] = v
        return items

    async def get_pattern(self, pattern: str):
        """根据给定的模式获取匹配的键值对。"""
        keys = await self.keys(pattern)
        return dict(zip(keys, await self.mget(keys), strict=False))

    async def del_pattern(self, pattern: str):
        """异步删除匹配指定模式的键。"""
        if keys := await self.keys(pattern):
            return await self.delete(*keys)
        else:
            return 0


redis = Redis(**REDIS)  # type: ignore
