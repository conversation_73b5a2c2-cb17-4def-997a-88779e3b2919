Page({
  data: {
    navBarBottom: 0,
    prices: {
      全通票: {
        单人: [50, 666],
        双人: [80, 666],
        三人: [120, 666],
        儿童: [30],
        亲子: [70],
        家庭: [100]
      },
      平日票: {
        单人: [50, 666],
        双人: [80, 666],
        三人: [120, 666],
        儿童: [30],
        亲子: [70],
        家庭: [100]
      }
    },
    sessions: [], // 场次列表
    selectedSession: null, // 当前选择的场次
    selectedTier: null, // 当前选择的票档
    selectedTierPrice: 0, // 当前选择票档的单价
    quantity: 1, // 购买数量
    totalPrice: 0, // 总价
    buttonText: '请选择场次', // 按钮文字
    buttonDisabled: true // 按钮是否禁用
  },

  onLoad(options) {
    // 获取导航栏高度
    this.setData({
      navBarBottom: wx.getMenuButtonBoundingClientRect().bottom
    });

    // 初始化场次列表
    this.setData({
      sessions: Object.keys(this.data.prices)
    });
  },

  // 选择场次
  selectSession(e) {
    const session = e.currentTarget.dataset.session;
    if (this.data.selectedSession === session) {
      // 如果重复点击已选中的场次，则取消选择
      this.setData({
        selectedSession: null,
        selectedTier: null,
        selectedTierPrice: 0,
        quantity: 1,
        totalPrice: 0,
        buttonText: '请选择场次',
        buttonDisabled: true
      });
    } else {
      this.setData({
        selectedSession: session,
        selectedTier: null, // 重置票档选择
        selectedTierPrice: 0,
        quantity: 1,
        totalPrice: 0,
        buttonText: '请选择票档',
        buttonDisabled: true
      });
    }
  },

  // 选择票档
  selectTier(e) {
    const tier = e.currentTarget.dataset.tier;
    const priceArray = this.data.prices[this.data.selectedSession][tier];
    const currentPrice = priceArray[0];

    if (this.data.selectedTier === tier) {
      // 如果重复点击已选中的票档，则取消选择
      this.setData({
        selectedTier: null,
        selectedTierPrice: 0,
        quantity: 1,
        totalPrice: 0,
        buttonText: '请选择票档',
        buttonDisabled: true
      });
    } else {
      this.setData({
        selectedTier: tier,
        selectedTierPrice: currentPrice,
        quantity: 1, // 默认数量为1
        totalPrice: currentPrice * 1,
        buttonText: '确认',
        buttonDisabled: false
      });
    }
  },

  // 数量减
  decreaseQuantity() {
    if (this.data.quantity > 1) {
      const newQuantity = this.data.quantity - 1;
      this.setData({
        quantity: newQuantity,
        totalPrice: this.data.selectedTierPrice * newQuantity
      });
    }
  },

  // 数量加
  increaseQuantity() {
    // 这里可以根据实际需求设置每单限购数量，例如 6
    if (this.data.quantity < 6) {
      const newQuantity = this.data.quantity + 1;
      this.setData({
        quantity: newQuantity,
        totalPrice: this.data.selectedTierPrice * newQuantity
      });
    } else {
      wx.showToast({
        title: '每单限购6份',
        icon: 'none'
      });
    }
  },

  // 处理购买
  handlePurchase() {
    if (!this.data.buttonDisabled) {
      // 执行购买逻辑，例如跳转到支付页面或显示确认信息
      console.log('购买信息:', {
        session: this.data.selectedSession,
        tier: this.data.selectedTier,
        quantity: this.data.quantity,
        totalPrice: this.data.totalPrice
      });
      wx.showToast({
        title: '购买成功（模拟）',
        icon: 'success'
      });
    }
  },

  // 返回上一页
  navigateBack: function () {
    wx.navigateBack();
  },

  onReady() {},
  onShow() {},
  onHide() {},
  onUnload() {},
  onPullDownRefresh() {},
  onReachBottom() {},
  onShareAppMessage() {}
});
