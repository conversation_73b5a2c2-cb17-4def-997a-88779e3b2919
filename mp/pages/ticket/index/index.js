Page({
  data: {
    selectedCity: '全国', // 初始选中的城市
    showCityPickerPopup: false, // 控制城市选择浮层显示
    searchText: '', // 搜索框内容
    allCities: [
      // 示例城市数据
      '北京',
      '保定',
      '成都',
      '重庆',
      '长沙',
      '广州',
      '桂林',
      '合肥',
      '哈尔滨',
      '济南',
      '嘉兴',
      '昆明',
      '南京',
      '南昌',
      '南宁',
      '上海',
      '深圳',
      '苏州',
      '沈阳',
      '天津',
      '武汉',
      '厦门',
      '西安',
      '郑州',
      '中山',
      '珠海'
    ],
    groupedCities: [], // 按字母分组的城市列表
    alphabet: [], // 字母索引
    scrollToLetter: '', // 用于scroll-view的scroll-into-view
    originalGroupedCities: [], // 保存原始的分组城市数据，用于搜索后恢复

    // 展览列表相关数据
    activeNav: 'digital_exhibition', // 当前主导航
    activeSubNavTag: '全部', // 当前子导航标签
    subNavBarTags: [], // 子导航标签列表
    allTickets: [], // 保存所有票据的原始列表
    tickets: [
      {
        id: 1,
        title: '【贵阳】丛林探秘•重返侏罗纪一大型恐龙主题VR沉浸体验（贵阳/THE ONE购物中心/恐龙）',
        addr: '贵阳·壹号购物中心',
        tags: ['重返侏罗纪'],
        price: [68, 168],
        notice: '入馆前请关注成都自然博物馆公众号，按照官方要求办理入馆流程，请知悉~\n祝您观展愉快！',
        thumbnail: 'https://mp.seamile.cn/static/mp/a.jpg',
        banner: 'https://mp.seamile.cn/static/mp/a.jpg',
        detail: [
          'https://mp.seamile.cn/static/mp/x-1.jpg',
          'https://mp.seamile.cn/static/mp/x-2.jpg',
          'https://mp.seamile.cn/static/mp/x-3.jpg',
          'https://mp.seamile.cn/static/mp/x-4.jpg',
          'https://mp.seamile.cn/static/mp/x-5.jpg',
          'https://mp.seamile.cn/static/mp/x-6.jpg',
          'https://mp.seamile.cn/static/mp/x-7.jpg'
        ]
      },
      {
        id: 2,
        title: '【滁州】宇宙猜想·启程&丛林探秘·熊猫的世界',
        addr: '滁州·吾悦广场',
        tags: ['宇宙猜想·启程', '熊猫的世界'],
        price: [68, 168],
        notice: '入馆前请关注成都自然博物馆公众号，按照官方要求办理入馆流程，请知悉~\n祝您观展愉快！',
        thumbnail: 'https://mp.seamile.cn/static/mp/b.jpg',
        banner: 'https://mp.seamile.cn/static/mp/b.jpg',
        detail: [
          'https://mp.seamile.cn/static/mp/x-1.jpg',
          'https://mp.seamile.cn/static/mp/x-2.jpg',
          'https://mp.seamile.cn/static/mp/x-3.jpg',
          'https://mp.seamile.cn/static/mp/x-4.jpg',
          'https://mp.seamile.cn/static/mp/x-5.jpg',
          'https://mp.seamile.cn/static/mp/x-6.jpg',
          'https://mp.seamile.cn/static/mp/x-7.jpg'
        ]
      },
      {
        id: 3,
        title: '【青岛】宇宙猜想·启程&丛林探秘·重返侏罗纪',
        addr: '青岛·金茂览秀城',
        tags: ['宇宙猜想·启程', '重返侏罗纪', '熊猫的世界'],
        price: [88, 188],
        notice: '入馆前请关注成都自然博物馆公众号，按照官方要求办理入馆流程，请知悉~\n祝您观展愉快！',
        thumbnail: 'https://mp.seamile.cn/static/mp/c.jpg',
        banner: 'https://mp.seamile.cn/static/mp/c.jpg',
        detail: [
          'https://mp.seamile.cn/static/mp/x-1.jpg',
          'https://mp.seamile.cn/static/mp/x-2.jpg',
          'https://mp.seamile.cn/static/mp/x-3.jpg',
          'https://mp.seamile.cn/static/mp/x-4.jpg',
          'https://mp.seamile.cn/static/mp/x-5.jpg',
          'https://mp.seamile.cn/static/mp/x-6.jpg',
          'https://mp.seamile.cn/static/mp/x-7.jpg'
        ]
      },
      {
        id: 4,
        title: '【宁波】丛林探秘·重返侏罗纪&丛林探秘·熊猫的世界',
        addr: '宁波鄞州区·宁波博物馆',
        tags: ['重返侏罗纪', '熊猫的世界'],
        price: [68, 168],
        notice: '入馆前请关注成都自然博物馆公众号，按照官方要求办理入馆流程，请知悉~\n祝您观展愉快！',
        thumbnail: 'https://mp.seamile.cn/static/mp/d.jpg',
        banner: 'https://mp.seamile.cn/static/mp/d.jpg',
        detail: [
          'https://mp.seamile.cn/static/mp/x-1.jpg',
          'https://mp.seamile.cn/static/mp/x-2.jpg',
          'https://mp.seamile.cn/static/mp/x-3.jpg',
          'https://mp.seamile.cn/static/mp/x-4.jpg',
          'https://mp.seamile.cn/static/mp/x-5.jpg',
          'https://mp.seamile.cn/static/mp/x-6.jpg',
          'https://mp.seamile.cn/static/mp/x-7.jpg'
        ]
      },
      {
        id: 5,
        title: '【贵阳】丛林探秘•重返侏罗纪一大型恐龙主题VR沉浸体验（贵阳/THE ONE购物中心/恐龙）',
        addr: '贵阳·壹号购物中心',
        tags: ['重返侏罗纪'],
        price: [68, 168],
        notice: '入馆前请关注成都自然博物馆公众号，按照官方要求办理入馆流程，请知悉~\n祝您观展愉快！',
        thumbnail: 'https://mp.seamile.cn/static/mp/e.jpg',
        banner: 'https://mp.seamile.cn/static/mp/e.jpg',
        detail: [
          'https://mp.seamile.cn/static/mp/x-1.jpg',
          'https://mp.seamile.cn/static/mp/x-2.jpg',
          'https://mp.seamile.cn/static/mp/x-3.jpg',
          'https://mp.seamile.cn/static/mp/x-4.jpg',
          'https://mp.seamile.cn/static/mp/x-5.jpg',
          'https://mp.seamile.cn/static/mp/x-6.jpg',
          'https://mp.seamile.cn/static/mp/x-7.jpg'
        ]
      },
      {
        id: 6,
        title: '【滁州】宇宙猜想·启程&丛林探秘·熊猫的世界',
        addr: '滁州·吾悦广场',
        tags: ['宇宙猜想·启程', '熊猫的世界'],
        price: [68, 168],
        notice: '入馆前请关注成都自然博物馆公众号，按照官方要求办理入馆流程，请知悉~\n祝您观展愉快！',
        thumbnail: 'https://mp.seamile.cn/static/mp/f.jpg',
        banner: 'https://mp.seamile.cn/static/mp/f.jpg',
        detail: [
          'https://mp.seamile.cn/static/mp/x-1.jpg',
          'https://mp.seamile.cn/static/mp/x-2.jpg',
          'https://mp.seamile.cn/static/mp/x-3.jpg',
          'https://mp.seamile.cn/static/mp/x-4.jpg',
          'https://mp.seamile.cn/static/mp/x-5.jpg',
          'https://mp.seamile.cn/static/mp/x-6.jpg',
          'https://mp.seamile.cn/static/mp/x-7.jpg'
        ]
      },
      {
        id: 7,
        title: '【青岛】探秘海昏侯国',
        addr: '青岛·金茂览秀城',
        tags: ['探秘海昏侯国'],
        price: [88, 188],
        notice: '入馆前请关注成都自然博物馆公众号，按照官方要求办理入馆流程，请知悉~\n祝您观展愉快！',
        thumbnail: 'https://mp.seamile.cn/static/mp/g.jpg',
        banner: 'https://mp.seamile.cn/static/mp/g.jpg',
        detail: [
          'https://mp.seamile.cn/static/mp/x-1.jpg',
          'https://mp.seamile.cn/static/mp/x-2.jpg',
          'https://mp.seamile.cn/static/mp/x-3.jpg',
          'https://mp.seamile.cn/static/mp/x-4.jpg',
          'https://mp.seamile.cn/static/mp/x-5.jpg',
          'https://mp.seamile.cn/static/mp/x-6.jpg',
          'https://mp.seamile.cn/static/mp/x-7.jpg'
        ]
      },
      {
        id: 8,
        title: '【宁波】丛林探秘·重返侏罗纪',
        addr: '宁波鄞州区·宁波博物馆',
        tags: ['重返侏罗纪'],
        price: [68, 168],
        notice: '入馆前请关注成都自然博物馆公众号，按照官方要求办理入馆流程，请知悉~\n祝您观展愉快！',
        thumbnail: 'https://mp.seamile.cn/static/mp/h.jpg',
        banner: 'https://mp.seamile.cn/static/mp/h.jpg',
        detail: [
          'https://mp.seamile.cn/static/mp/x-1.jpg',
          'https://mp.seamile.cn/static/mp/x-2.jpg',
          'https://mp.seamile.cn/static/mp/x-3.jpg',
          'https://mp.seamile.cn/static/mp/x-4.jpg',
          'https://mp.seamile.cn/static/mp/x-5.jpg',
          'https://mp.seamile.cn/static/mp/x-6.jpg',
          'https://mp.seamile.cn/static/mp/x-7.jpg'
        ]
      }
    ]
  },

  onLoad: function (options) {
    this.processCities();
    this.processCities(); // 这行原本就在，保持
    // app.globalData.allTickets = JSON.parse(JSON.stringify(this.data.tickets)); // 移除：不再需要将票务数据存入全局
    this.setData({
      allTickets: JSON.parse(JSON.stringify(this.data.tickets)) // 页面本身可能仍需要 allTickets 来进行过滤操作
    });
    this.processSubNavTags(); // 处理子导航标签
    // 如果有加载票务的逻辑，也应在此处调用
    // this.loadTickets();
  },

  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      });
    }
  },

  // 处理城市数据，进行分组和生成字母表
  processCities: function () {
    const getFirstLetter = (str) => {
      if (!str) return '#';
      const char = str.charAt(0);
      const pinyinMap = {
        // 极简拼音映射，仅供演示
        北京: 'B',
        保定: 'B',
        成都: 'C',
        滁州: 'C',
        常州: 'C',
        重庆: 'C',
        长沙: 'C',
        东莞: 'D',
        大连: 'D',
        佛山: 'F',
        福州: 'F',
        广州: 'G',
        贵阳: 'G',
        桂林: 'G',
        杭州: 'H',
        合肥: 'H',
        哈尔滨: 'H',
        济南: 'J',
        嘉兴: 'J',
        昆明: 'K',
        廊坊: 'L',
        临沂: 'L',
        南京: 'N',
        宁波: 'N',
        南昌: 'N',
        南宁: 'N',
        青岛: 'Q',
        泉州: 'Q',
        上海: 'S',
        深圳: 'S',
        苏州: 'S',
        沈阳: 'S',
        石家庄: 'S',
        天津: 'T',
        太原: 'T',
        唐山: 'T',
        武汉: 'W',
        无锡: 'W',
        温州: 'W',
        厦门: 'X',
        西安: 'X',
        徐州: 'X',
        烟台: 'Y',
        扬州: 'Y',
        郑州: 'Z',
        中山: 'Z',
        珠海: 'Z',
        淄博: 'Z'
      };
      if (pinyinMap[str]) return pinyinMap[str];
      if (/[a-zA-Z]/.test(char)) return char.toUpperCase();
      return '#';
    };

    const cities = this.data.allCities.sort((a, b) => {
      const letterA = getFirstLetter(a);
      const letterB = getFirstLetter(b);
      if (letterA < letterB) return -1;
      if (letterA > letterB) return 1;
      return a.localeCompare(b, 'zh-CN');
    });

    const grouped = {};
    cities.forEach((city) => {
      const letter = getFirstLetter(city);
      if (!grouped[letter]) {
        grouped[letter] = [];
      }
      grouped[letter].push(city);
    });

    const groupedCities = Object.keys(grouped)
      .sort()
      .map((letter) => ({
        letter: letter,
        cities: grouped[letter]
      }));
    const alphabet = groupedCities.map((group) => group.letter);
    this.setData({
      groupedCities: groupedCities,
      originalGroupedCities: JSON.parse(JSON.stringify(groupedCities)),
      alphabet: alphabet
    });
  },

  openCityPicker: function () {
    this.setData({ showCityPickerPopup: true });
  },

  closeCityPicker: function () {
    this.setData({ showCityPickerPopup: false, searchText: '' });
    this.setData({ groupedCities: this.data.originalGroupedCities });
  },

  onSearchInput: function (e) {
    const searchText = e.detail.value.trim();
    this.setData({ searchText: searchText });
    if (!searchText) {
      this.setData({ groupedCities: this.data.originalGroupedCities });
      return;
    }
    const filteredGroupedCities = this.data.originalGroupedCities
      .map((group) => {
        const filteredCities = group.cities.filter((city) => city.includes(searchText));
        return { ...group, cities: filteredCities };
      })
      .filter((group) => group.cities.length > 0);
    this.setData({ groupedCities: filteredGroupedCities });
  },

  selectCurrentCity: function (e) {
    const city = e.currentTarget.dataset.city;
    const finalCity = city === '全部城市' ? '全国' : city;
    this.setData({
      selectedCity: finalCity,
      showCityPickerPopup: false,
      searchText: ''
    });
    this.setData({ groupedCities: this.data.originalGroupedCities });
    console.log('当前选择城市:', finalCity, '，应根据此城市刷新展览列表');
    // this.loadTickets({ city: finalCity }); // 示例：刷新票务
  },

  onCityTap: function (e) {
    const city = e.currentTarget.dataset.city;
    this.setData({
      selectedCity: city,
      showCityPickerPopup: false,
      searchText: ''
    });
    this.setData({ groupedCities: this.data.originalGroupedCities });
    console.log('选择了城市:', city, '，应根据此城市刷新展览列表');
    // this.loadTickets({ city: city }); // 示例：刷新票务
  },

  scrollToLetter: function (e) {
    const letter = e.currentTarget.dataset.letter;
    this.setData({ scrollToLetter: 'letter-' + letter });
  },

  // 导航栏点击事件
  onNavTap: function (e) {
    const navType = e.currentTarget.dataset.type;
    if (this.data.activeNav === navType) {
      return;
    }
    this.setData({
      activeNav: navType
    });
    console.log('切换导航:', navType, '，应根据此分类和当前城市刷新展览列表');
    // this.loadTickets({ category: navType, city: this.data.selectedCity }); // 示例：刷新票务
  },

  // 处理子导航标签数据
  processSubNavTags: function () {
    // 使用 allTickets 生成标签，确保标签完整性不受当前列表过滤影响
    const allTags = (this.data.allTickets || []).reduce((acc, ticket) => {
      if (ticket.tags && ticket.tags.length > 0) {
        ticket.tags.forEach((tag) => {
          if (!acc.includes(tag)) {
            acc.push(tag);
          }
        });
      }
      return acc;
    }, []);

    this.setData({
      subNavBarTags: ['全部', ...allTags]
    });
  },

  // 子导航栏点击事件
  onSubNavTap: function (e) {
    const tagName = e.currentTarget.dataset.tag;
    if (this.data.activeSubNavTag === tagName) {
      return;
    }
    this.setData({
      activeSubNavTag: tagName
    });

    if (tagName === '全部') {
      this.setData({
        tickets: this.data.allTickets
      });
    } else {
      const filteredTickets = this.data.allTickets.filter((ticket) => {
        return ticket.tags && ticket.tags.includes(tagName);
      });
      this.setData({
        tickets: filteredTickets
      });
    }
    console.log('切换子导航:', tagName, '，列表已根据标签过滤');
  },

  goToTicketDetail: function (e) {
    const ticketId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/ticket/detail/detail?id=${ticketId}`
    });
  }

  // loadTickets: function(params = {}) {
  //   console.log("Loading tickets with params:", params);
  //   // 实际加载票务数据的逻辑
  // }
});
