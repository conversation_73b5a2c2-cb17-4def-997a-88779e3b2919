Page({
  data: {
    showNoticeDialog: false, // 控制通知弹窗显示

    ticketDetail: {
      id: 1,
      title: '【贵阳】丛林探秘•重返侏罗纪一大型恐龙主题VR沉浸体验（贵阳/THE ONE购物中心/恐龙）',
      addr: '贵阳·壹号购物中心',
      tags: ['重返侏罗纪'],
      price: [68, 168],
      notice: '入馆前请关注成都自然博物馆公众号，按照官方要求办理入馆流程，请知悉~\n祝您观展愉快！',
      features: {
        refundable: false, // 是否可退
        source: 'office', // 票源
        electronic: true, // 是否电子票
        invoice: `需要开具发票的购票用户，请您在演出开始5日前，电话联系客服（40008822）并提供开票信息，\
                  展期结束后将统-由演出主办单位开具` // 发票信息
      },
      thumbnail: 'https://mp.seamile.cn/static/mp/e.jpg',
      banner: 'https://mp.seamile.cn/static/mp/f.jpg',
      detail: [
        'https://mp.seamile.cn/static/mp/x-1.jpg',
        'https://mp.seamile.cn/static/mp/x-2.jpg',
        'https://mp.seamile.cn/static/mp/x-3.jpg',
        'https://mp.seamile.cn/static/mp/x-4.jpg',
        'https://mp.seamile.cn/static/mp/x-5.jpg',
        'https://mp.seamile.cn/static/mp/x-6.jpg',
        'https://mp.seamile.cn/static/mp/x-7.jpg'
      ]
    }
  },

  onLoad: function (options) {
    // 数据已在 data 中直接定义，这里仅设置导航栏标题
    // options.id 仍然可以从列表页传递过来，但此处不再用于查找数据
    const ticket = this.data.ticketDetail;
    if (ticket && ticket.title) {
      let navTitle = '展览详情'; // 默认标题
      const titleParts = ticket.title.split('】');
      if (titleParts.length > 1) {
        const mainTitle = titleParts[1].split('（')[0];
        if (mainTitle) {
          navTitle = mainTitle;
        }
      } else {
        const simpleTitleParts = ticket.title.split('（')[0];
        if (simpleTitleParts) {
          navTitle = simpleTitleParts;
        }
      }
      wx.setNavigationBarTitle({
        title: navTitle
      });
    } else {
      wx.setNavigationBarTitle({
        title: '展览详情'
      });
      console.warn('ticketDetail.title 未定义，无法设置动态导航栏标题');
    }

    // 如果notice不为空，显示通知弹窗
    if (ticket && ticket.notice) {
      this.setData({
        showNoticeDialog: true
      });
    }
  },

  showErrorAndNavigateBack: function (title) {
    wx.showToast({
      title: title,
      icon: 'none',
      duration: 2000,
      complete: () => {
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
      }
    });
  },

  // 返回上一页
  navigateBack: function () {
    wx.navigateBack();
  },

  // 购票按钮点击事件
  goToTicketPurchase: function () {
    wx.navigateTo({
      url: '/pages/ticket/purchase/purchase'
    });
  },

  // AR按钮点击事件
  goToTicketAR: function () {
    // AR相关逻辑
    if (!this.data.ticketDetail) return;
    wx.showToast({
      title: 'AR功能开发中，敬请期待',
      icon: 'none'
    });
  },

  // 处理通知弹窗确认事件
  onNoticeDialogConfirm: function () {
    this.setData({
      showNoticeDialog: false
    });
  },

  // 导航到特征详情页 (Placeholder)
  goToTicketFeatures: function () {
    wx.navigateTo({
      url: '/pages/ticket/features/features'
    });
  }
});
