@import '/app.wxss';

.container {
  padding-bottom: 0;
}

.item-list {
  padding-bottom: 60rpx;
}

.item-title {
  display: block;
  font-size: 24rpx;
  line-height: 1.4;
  overflow: visible;
  text-overflow: visible;
}

/* 表单区域 */
.form-section {
  margin-bottom: 60rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--dark);
  margin-bottom: 40rpx;
}

/* 日期选择 */
.date-options {
  display: flex;
  gap: 20rpx;
}

.date-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  border: 2rpx solid var(--silver);
  border-radius: 16rpx;
  background: white;
  transition: all 0.3s ease;
}

.date-option.active {
  border-color: var(--primary);
  background: var(--lightblue);
}

.date-option.custom {
  position: relative;
}

.date-label {
  font-size: 24rpx;
  color: var(--medium);
  margin-bottom: 10rpx;
}

.date-value {
  font-size: 28rpx;
  color: var(--dark);
  font-weight: 500;
}

.arrow-icon {
  position: absolute;
  top: 50%;
  right: 20rpx;
  transform: translateY(-50%);
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 表单项 */
.form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 0;
  border-bottom: 1rpx solid var(--silver);
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 32rpx;
  color: var(--dark);
}

.form-value {
  font-size: 32rpx;
  color: var(--medium);
}

/* 计数器 */
.form-counter {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.counter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: var(--primary);
  transition: all 0.3s ease;
}

.counter-btn.disabled {
  background: var(--silver);
  opacity: 0.5;
}

.counter-icon {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1);
}

.counter-value {
  font-size: 32rpx;
  color: var(--dark);
  min-width: 80rpx;
  text-align: center;
}

/* 按钮区域 */
.form-actions {
  margin-top: 80rpx;
}

.btn-block {
  width: 100%;
  padding: 30rpx;
  font-size: 32rpx;
}

/* 日期选择器弹窗 */
.date-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--translucence);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.date-picker-overlay.show {
  opacity: 1;
  visibility: visible;
}

.date-picker-popup {
  width: 100%;
  background: white;
  border-radius: 40rpx 40rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.date-picker-overlay.show .date-picker-popup {
  transform: translateY(0);
}

.date-picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 60rpx;
  border-bottom: 1rpx solid var(--silver);
}

.date-picker-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--dark);
}

.date-picker-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
}

.close-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

.date-picker-content {
  padding: 40rpx 60rpx 80rpx;
}

/* 日历 */
.calendar-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.weekday {
  text-align: center;
  font-size: 28rpx;
  color: var(--medium);
  padding: 20rpx 0;
}

.calendar-month {
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: var(--dark);
  margin-bottom: 40rpx;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 20rpx;
}

.calendar-day {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 28rpx;
  color: var(--dark);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.calendar-day.disabled {
  color: var(--bright);
  pointer-events: none;
}

.calendar-day.selected {
  background: var(--primary);
  color: white;
}

.calendar-day:not(.disabled):not(.selected):hover {
  background: var(--lightblue);
}
