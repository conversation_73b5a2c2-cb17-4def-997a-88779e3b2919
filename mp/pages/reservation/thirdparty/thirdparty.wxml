<view class="container">
  <!-- 当前选择的观展项目 -->
  <view class="item-list">
    <view class="item">
      <image class="item-thumbnail"
             src="{{selectedExhibition.thumbnail}}"
             mode="aspectFill"></image>
      <view class="item-content">
        <view class="item-title">{{selectedExhibition.title}}</view>
        <view class="item-desc">{{selectedExhibition.period}}</view>
      </view>
    </view>
  </view>

  <!-- 预约表单 -->
  <view class="main-card">
    <view class="section">
      <!-- 预约日期 -->
      <view class="form-section">
        <view class="section-title">预约日期</view>
        <view class="date-options">
          <view class="date-option {{selectedDate === 'today' ? 'active' : ''}}"
                bindtap="selectDate" data-date="today">
            <view class="date-label">今天</view>
            <view class="date-value">05月28日</view>
          </view>
          <view class="date-option {{selectedDate === 'tomorrow' ? 'active' : ''}}"
                bindtap="selectDate" data-date="tomorrow">
            <view class="date-label">明天</view>
            <view class="date-value">05月29日</view>
          </view>
          <view class="date-option {{selectedDate === 'dayafter' ? 'active' : ''}}"
                bindtap="selectDate" data-date="dayafter">
            <view class="date-label">后天</view>
            <view class="date-value">05月30日</view>
          </view>
          <view class="date-option custom {{selectedDate === 'custom' ? 'active' : ''}}"
                bindtap="showDatePicker">
            <view class="date-label">指定</view>
            <view class="date-value">日期</view>
            <image class="arrow-icon" src="/assets/icons/right-solid.svg"></image>
          </view>
        </view>
      </view>

      <!-- 预约信息 -->
      <view class="form-section">
        <view class="section-title">预约信息</view>
        <view class="form-item">
          <view class="form-label">预约日期</view>
          <view class="form-value">{{displayDate}}</view>
        </view>
        <view class="form-item">
          <view class="form-label">预约人数</view>
          <view class="form-counter">
            <view class="counter-btn {{reservationCount <= 1 ? 'disabled' : ''}}"
                  bindtap="decreaseCount">
              <image class="counter-icon" src="/assets/icons/minus.svg"></image>
            </view>
            <view class="counter-value">{{reservationCount}}人</view>
            <view class="counter-btn" bindtap="increaseCount">
              <image class="counter-icon" src="/assets/icons/plus.svg"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 下一步按钮 -->
      <view class="form-actions">
        <button class="btn btn-primary btn-block" bindtap="nextStep">下一步</button>
      </view>
    </view>
  </view>

  <!-- 日期选择器弹窗 -->
  <view class="date-picker-overlay {{showDatePicker ? 'show' : ''}}"
        bindtap="hideDatePicker">
    <view class="date-picker-popup" catchtap="stopPropagation">
      <view class="date-picker-header">
        <view class="date-picker-title">请选择预约日期</view>
        <view class="date-picker-close" bindtap="hideDatePicker">
          <image class="close-icon" src="/assets/icons/close.svg"></image>
        </view>
      </view>
      <view class="date-picker-content">
        <view class="calendar-header">
          <view class="weekday">一</view>
          <view class="weekday">二</view>
          <view class="weekday">三</view>
          <view class="weekday">四</view>
          <view class="weekday">五</view>
          <view class="weekday">六</view>
          <view class="weekday">日</view>
        </view>
        <view class="calendar-month">{{currentMonth}}</view>
        <view class="calendar-grid">
          <view class="calendar-day {{item.disabled ? 'disabled' : ''}} {{item.selected ? 'selected' : ''}}"
                wx:for="{{calendarDays}}" wx:key="index"
                bindtap="selectCalendarDate" data-date="{{item.date}}">
            {{item.day}}
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
