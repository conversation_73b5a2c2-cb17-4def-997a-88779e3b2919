// pages/reservation/thirdparty/thirdparty.js
Page({
  data: {
    selectedExhibition: null,
    selectedDate: '', // 当前选择的日期类型
    displayDate: '', // 显示的日期文本
    reservationCount: 3, // 预约人数
    showDatePicker: false, // 是否显示日期选择器
    currentMonth: '2025年5月',
    calendarDays: [] // 日历天数数据
  },

  onLoad(options) {
    // 从URL参数获取展览ID
    const exhibitionId = options.id;
    if (exhibitionId) {
      this.loadExhibitionData(exhibitionId);
    }

    // 初始化日期
    this.initializeDates();
    // 生成日历数据
    this.generateCalendar();
  },

  // 加载展览数据
  loadExhibitionData(id) {
    // 模拟数据，实际应该从API获取
    const exhibitions = [
      {
        id: 1,
        title: '【新疆】宇宙猜想·启程&丛林探险·重返侏罗纪&丛林探险·熊猫的世界（乌鲁木齐/新疆科技馆/宇宙&熊猫&恐龙）',
        period: '2025.04.25 - 2026.02.28',
        thumbnail: 'https://mp.seamile.cn/static/mp/a.jpg'
      },
      {
        id: 2,
        title: '【南昌】探秘海昏侯国-华夏文明数字体验展',
        period: '常驻展',
        thumbnail: 'https://mp.seamile.cn/static/mp/b.jpg'
      }
    ];

    const exhibition = exhibitions.find((item) => item.id == id);
    if (exhibition) {
      this.setData({
        selectedExhibition: exhibition
      });
    }
  },

  // 初始化日期
  initializeDates() {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    const dayAfter = new Date(today);
    dayAfter.setDate(today.getDate() + 2);

    // 默认选择今天
    this.setData({
      selectedDate: 'today',
      displayDate: this.formatDate(today)
    });
  },

  // 格式化日期
  formatDate(date) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}月${day}日`;
  },

  // 选择日期
  selectDate(e) {
    const dateType = e.currentTarget.dataset.date;
    const today = new Date();
    let selectedDate;
    let displayDate;

    switch (dateType) {
      case 'today':
        selectedDate = today;
        displayDate = this.formatDate(today);
        break;
      case 'tomorrow':
        selectedDate = new Date(today);
        selectedDate.setDate(today.getDate() + 1);
        displayDate = this.formatDate(selectedDate);
        break;
      case 'dayafter':
        selectedDate = new Date(today);
        selectedDate.setDate(today.getDate() + 2);
        displayDate = this.formatDate(selectedDate);
        break;
    }

    this.setData({
      selectedDate: dateType,
      displayDate: displayDate
    });
  },

  // 显示日期选择器
  showDatePicker() {
    this.setData({
      showDatePicker: true
    });
  },

  // 隐藏日期选择器
  hideDatePicker() {
    this.setData({
      showDatePicker: false
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击弹窗内容时关闭弹窗
  },

  // 生成日历数据
  generateCalendar() {
    const year = 2025;
    const month = 4; // 5月 (0-based)
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);

    // 调整到周一开始
    const dayOfWeek = firstDay.getDay();
    const mondayOffset = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
    startDate.setDate(firstDay.getDate() - mondayOffset);

    const calendarDays = [];
    const today = new Date();

    // 生成6周的日期
    for (let i = 0; i < 42; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);

      const isCurrentMonth = currentDate.getMonth() === month;
      const isPastDate = currentDate < today;

      calendarDays.push({
        date: currentDate.toISOString().split('T')[0],
        day: currentDate.getDate(),
        disabled: !isCurrentMonth || isPastDate,
        selected: false
      });
    }

    this.setData({
      calendarDays: calendarDays
    });
  },

  // 选择日历日期
  selectCalendarDate(e) {
    const selectedDate = e.currentTarget.dataset.date;
    const date = new Date(selectedDate);

    // 更新日历显示
    const calendarDays = this.data.calendarDays.map((day) => ({
      ...day,
      selected: day.date === selectedDate
    }));

    this.setData({
      calendarDays: calendarDays,
      selectedDate: 'custom',
      displayDate: this.formatDate(date),
      showDatePicker: false
    });
  },

  // 减少人数
  decreaseCount() {
    if (this.data.reservationCount > 1) {
      this.setData({
        reservationCount: this.data.reservationCount - 1
      });
    }
  },

  // 增加人数
  increaseCount() {
    this.setData({
      reservationCount: this.data.reservationCount + 1
    });
  },

  // 下一步
  nextStep() {
    if (!this.data.selectedDate) {
      wx.showToast({
        title: '请选择预约日期',
        icon: 'none'
      });
      return;
    }

    wx.showToast({
      title: '预约信息已提交',
      icon: 'success'
    });

    // 这里可以添加实际的预约提交逻辑
    // 例如跳转到确认页面或返回上一页
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  }
});
