@import '/app.wxss';

.item-list {
  padding-bottom: 60rpx;
}

.item-title {
  display: block;
  font-size: 24rpx;
  line-height: 1.4;
  overflow: visible;
  text-overflow: visible;
}

/* 预约类型选择标题 */
.choice-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--dark);
  margin-bottom: 40rpx;
}

/* 预约选项 */
.choice-option {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  border-radius: 20rpx;
}

.miniprogram-option {
  background: linear-gradient(135deg, var(--primary) 30%, var(--tertiary) 100%);
  color: white;
}

.other-option {
  background: linear-gradient(135deg, #ff9a56 30%, #ffad56 100%);
  color: white;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.option-desc {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.4;
}

.option-arrow {
  margin-left: 20rpx;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1);
}
