// pages/reservation/choice/choice.js
Page({
  data: {
    selectedExhibition: null
  },

  onLoad(options) {
    // 从URL参数获取展览ID
    const exhibitionId = options.id;
    if (exhibitionId) {
      this.loadExhibitionData(exhibitionId);
    }
  },

  // 加载展览数据
  loadExhibitionData(id) {
    // 模拟数据，实际应该从API获取
    const exhibitions = [
      {
        id: 1,
        title: '【新疆】宇宙猜想·启程&丛林探险·重返侏罗纪&丛林探险·熊猫的世界（乌鲁木齐/新疆科技馆/宇宙&熊猫&恐龙）',
        period: '2025.04.25 - 2026.02.28',
        thumbnail: 'https://mp.seamile.cn/static/mp/a.jpg'
      },
      {
        id: 2,
        title: '【南昌】探秘海昏侯国-华夏文明数字体验展',
        period: '常驻展',
        thumbnail: 'https://mp.seamile.cn/static/mp/b.jpg'
      }
    ];

    const exhibition = exhibitions.find((item) => item.id == id);
    if (exhibition) {
      this.setData({
        selectedExhibition: exhibition
      });
    }
  },

  // 选择小程序购票预约
  selectMiniProgramReservation() {
    if (this.data.selectedExhibition) {
      wx.navigateTo({
        url: `/pages/reservation/purchase/purchase?id=${this.data.selectedExhibition.id}`
      });
    } else {
      wx.showToast({
        title: '请先选择展览',
        icon: 'none'
      });
    }
  },

  // 选择其它渠道预约
  selectOtherChannelReservation() {
    wx.showToast({
      title: '跳转到预约表单',
      icon: 'none'
    });
    // 实际应该跳转到预约表单页面
    // wx.navigateTo({
    //   url: `/pages/reservation/form/form?id=${this.data.selectedExhibition.id}&type=other`
    // });
  }
});
