<view class="container">
  <navbar />

  <!-- 页面标题区域 -->
  <view class="main-banner" style="padding-top: {{navBarBottom}}px;">
    <view class="section">
      <text class="ticket-title">{{exhibitionTitle}}</text>
    </view>
  </view>

  <!-- 主要内容区域，包含场次和票档选择 -->
  <view class="main-card">
    <!-- 场次选择 -->
    <view class="section">
      <text class="section-title">场次</text>
      <view class="options-list">
        <block wx:for="{{sessions}}" wx:key="*this">
          <view
            class="option-item {{ selectedSession === item ? 'selected' : '' }}"
            data-session="{{item}}"
            bindtap="selectSession"
          >
            <text class="option-text">{{item}}</text>
          </view>
        </block>
      </view>
    </view>

    <!-- 票档选择 -->
    <block wx:if="{{selectedSession}}">
      <view class="section">
        <text class="section-title">票档</text>
        <view class="options-list tier-options-list">
          <block wx:for="{{prices[selectedSession]}}" wx:for-item="priceInfo" wx:for-index="tierName" wx:key="tierName">
            <view
              class="option-item tier-option-item {{ selectedTier === tierName ? 'selected' : '' }}"
              data-tier="{{tierName}}"
              bindtap="selectTier"
            >
              <text class="option-text tier-name-text">{{tierName}}</text>
              <text class="option-text tier-price-text space-left">¥ {{priceInfo[0]}}</text>
              <text wx:if="{{priceInfo.length > 1}}" class="tier-original-price-text space-left">（原价¥{{priceInfo[1]}}）</text>
            </view>
          </block>
        </view>
      </view>
    </block>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <block wx:if="{{selectedTier}}">
      <!-- 数量选择器 -->
      <view class="quantity-section-wrapper">
        <view class="quantity-text-info">
          <text class="quantity-label">数量</text>
          <text class="quantity-limit">每单限购6份</text>
        </view>

        <view class="quantity-controls">
          <view class="quantity-btn {{ quantity === 1 ? 'disabled' : '' }}" bindtap="decreaseQuantity">-</view>
          <text class="quantity-value">{{quantity}}份</text>
          <view class="quantity-btn {{ quantity > 5 ? 'disabled' : ''}}" bindtap="increaseQuantity">+</view>
        </view>
      </view>

      <!-- 总价、确认按钮 -->
      <view class="bottom-action-row">
        <view class="price-summary">
          <text class="total-price-label">总价</text>
          <text class="total-price-value">¥{{totalPrice}}</text>
        </view>
        <view>
          <button
            class="purchase-button btn {{buttonDisabled ? 'disabled' : ''}}"
            bindtap="handlePurchase"
            disabled="{{buttonDisabled}}"
          >
          {{buttonText}}
        </button>
        </view>
      </view>
    </block>
    <block wx:else>
       <view class="bottom-action-row">
        <view class="price-summary"></view>
        <view>
          <button class="purchase-button btn disabled" disabled="true">
            {{buttonText}}
          </button>
        </view>
      </view>
    </block>
  </view>
</view>
