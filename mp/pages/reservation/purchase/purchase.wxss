@import '/app.wxss';

.container {
  background-color: white;
}

.main-banner {
  padding-bottom: 2rem;
}

.ticket-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  line-height: 1.5;
}

.section-title {
  display: block;
  font-size: 28rpx;
  color: var(--dark);
  margin-bottom: 25rpx;
  font-weight: bold;
}

.options-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.option-item {
  background-color: var(--paper);
  border: 1rpx solid var(--silver);
  border-radius: 100rpx;
  padding: 18rpx 35rpx;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.option-text {
  font-size: 28rpx;
  color: var(--dark);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.option-item.selected {
  background-color: var(--silver);
  border-color: var(--primary);
}

.option-item.selected .option-text {
  color: var(--primary);
  font-weight: bold;
}

.tier-options-list .option-item {
  flex-direction: row;
  align-items: baseline;
  justify-content: flex-start;
  padding: 18rpx 25rpx;
  width: auto;
  flex-grow: 0;
}

.tier-option-item.selected .tier-name-text,
.tier-option-item.selected .tier-price-text {
  font-weight: bold;
}

.space-left {
  margin-left: 10rpx;
}

.tier-original-price-text {
  font-size: 22rpx;
  color: var(--light);
  text-decoration: line-through;
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid var(--silver);
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  z-index: 10;
}

.quantity-section-wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quantity-text-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.quantity-label {
  font-size: 28rpx;
  color: var(--dark);
}

.quantity-limit {
  font-size: 22rpx;
  color: var(--light);
  margin-top: 5rpx;
}

.quantity-controls {
  display: flex;
  align-items: center;
  background-color: var(--paper);
  border-radius: 50rpx;
  overflow: hidden;
}

.quantity-btn {
  width: 50rpx;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  font-size: 32rpx;
  color: var(--dark);
  background-color: var(--bright);
}

.quantity-btn.disabled {
  color: var(--bright);
  background-color: var(--silver);
}

.quantity-value {
  padding: 0 20rpx;
  font-size: 28rpx;
}

.bottom-action-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.price-summary {
  display: flex;
  align-items: baseline;
  flex-shrink: 0;
}

.total-price-label {
  font-size: 30rpx;
  color: var(--dark);
}

.total-price-value {
  font-size: 50rpx;
  color: var(--softred);
  font-weight: bold;
  margin-left: 10rpx;
}

.purchase-button {
  display: flex;
  padding-left: 60rpx;
  padding-right: 60rpx;
  flex-shrink: 0;
}

.purchase-button.disabled {
  background-color: var(--bright) !important;
  color: var(--light) !important;
  border-color: var(--bright) !important;
}
