<view class="container">
  <!-- 预约列表 -->
  <view class="item-list">
    <view class="item" wx:for="{{reservations}}" wx:key="id">
      <image class="item-thumbnail" src="{{item.thumbnail}}" mode="aspectFill"></image>
      <view class="item-content">
        <view class="item-title">{{item.title}}</view>
        <view class="item-desc">展期：{{item.period}}</view>
        <view class="item-desc">地点：{{item.location}}</view>
        <view class="item-spec">
          <view class="action">
            <button class="btn btn-sm" bindtap="goToReservationChoice" data-id="{{item.id}}">去预约</button>
          </view>
        </view>
      </view>
    </view>

    <view wx:if="{{reservations.length === 0}}" class="empty">
      暂无可预约的展览
    </view>
  </view>
</view>
