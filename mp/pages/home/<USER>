/* pages/home/<USER>/

@import '/app.wxss';

.container {
  padding-top: 200rpx;
}

/* 标题 */
.navbar-title {
  font-weight: bold;
  color: white;
  filter: drop-shadow(0 0 2px var(--translucence));
}

.logo {
  height: 30rpx;
  width: 30rpx;
  margin-right: 5rpx;
}

/* 1. 轮播区域 */
.swiper-container {
  width: 100%;
  height: 500rpx;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

/* 轮播图片 */
.swiper-image {
  width: 100%;
  height: 100%;
}

/* 调整轮小圆点 */
.swiper-container .wx-swiper-dots {
  bottom: 70rpx;
}

/* 2. 导航区 */
.nav-section {
  display: flex;
  justify-content: space-around;
  position: relative;
  width: 95%;
  background-color: white;
  padding: 30rpx;
  z-index: 10;
  margin-top: 250rpx;
  border-radius: 20rpx;
  box-sizing: border-box;
  box-shadow: 0 0 10rpx var(--shadow);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 24rpx;
  color: var(--dark);
}

.nav-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

/* 3. 热展推荐区 */
.hot-exhibitions-section {
  width: 95%;
  margin: 10rpx auto;
  box-sizing: border-box;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--dark);
}

.section-more {
  font-size: 24rpx;
  color: var(--light);
}

.exhibition-list {
  display: flex;
  flex-direction: column;
}

.exhibition-item {
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 0 10rpx var(--shadow);
}

.exhibition-image {
  width: 100%;
  height: 390rpx;
  display: block;
}
