Page({
  data: {
    navBarTop: 0,
    navBarHeight: 0,
    hotExhibitions: [
      { id: 3, banner: 'https://mp.seamile.cn/static/mp/c.jpg' },
      { id: 4, banner: 'https://mp.seamile.cn/static/mp/d.jpg' },
      { id: 5, banner: 'https://mp.seamile.cn/static/mp/e.jpg' },
      { id: 6, banner: 'https://mp.seamile.cn/static/mp/f.jpg' },
      { id: 7, banner: 'https://mp.seamile.cn/static/mp/g.jpg' },
      { id: 8, banner: 'https://mp.seamile.cn/static/mp/h.jpg' }
    ]
  },
  onLoad() {
    // 获取导航栏高度
    const menuBtn = wx.getMenuButtonBoundingClientRect();
    this.setData({
      navBarTop: menuBtn.top,
      navBarHeight: menuBtn.height
    });
  },
  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      });
    }
  },
  goToTicketIndex() {
    wx.switchTab({
      url: '/pages/ticket/index/index'
    });
  },

  goToTicketDetail(event) {
    const exhibitionId = event.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/ticket/detail/detail?id=${exhibitionId}`
    });
  }
});
