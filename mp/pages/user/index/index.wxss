@import '/app.wxss';

.main-banner {
  height: 30vh;
  background: linear-gradient(135deg, var(--secondary) 0%, var(--primary) 100%);
}

.main-card {
  background-color: var(--paper);
}

.profile-section {
  position: relative;
  display: flex;
  margin-top: -160rpx;
  align-items: center;
  flex-direction: column;
}

.avatar-wrapper {
  display: flex;
  width: 210rpx;
  height: 210rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--tertiary);
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.avatar {
  width: 180rpx;
  height: 180rpx;
  padding-top: 50rpx;
}

.nickname {
  font-size: 37rpx;
  font-weight: 700;
  color: var(--primary);
}



.menu-section {
  width: 95vw;
  margin: 10rpx auto 0 auto;
  border-radius: 25rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  margin: 30rpx 5rpx;
  background-color: white;
  border-radius: 25rpx;
  box-shadow: 0 0 5rpx var(--shadow);
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item text {
  font-size: 32rpx;
  color: var(--dark);
  font-weight: 400;
}

.arrow-right {
  width: 30rpx;
  height: 30rpx;
}
