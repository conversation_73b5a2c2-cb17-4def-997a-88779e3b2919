Page({
  data: {},
  onLoad: function (options) {},
  onReady: function () {},
  onShow: function () {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 4
      });
    }
  },
  onHide: function () {},
  onUnload: function () {},
  onPullDownRefresh: function () {},
  onReachBottom: function () {},
  onShareAppMessage: function () {},

  // 导航到用户须知页面
  goToUserNotice: function () {
    wx.navigateTo({
      url: '/pages/user/notice/notice'
    });
  }
});
