.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding-bottom: 20rpx;
  background: white;
  display: flex;
  border-radius: 50rpx 50rpx 0 0;
  box-shadow: 0 -1rpx 5rpx rgba(0, 0, 0, 0.1);
  overflow: visible;
  z-index: 9999;
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;
  height: 160rpx;
  padding-bottom: 30rpx;
  box-sizing: border-box;
}

.tab-bar-item.special {
  position: relative;
}

.tab-bar-item-image {
  width: 40rpx;
  height: 40rpx;
}

.tab-bar-item-image.special-image {
  width: 150rpx;
  height: 150rpx;
  background: #4b4ee8;
  border-radius: 50%;
  padding: 37rpx;
  box-sizing: border-box;
  position: absolute;
  top: -37rpx;
  border: 10rpx solid white;
}

.tab-bar-item-text {
  font-size: 24rpx;
  margin-top: 10rpx;
}
