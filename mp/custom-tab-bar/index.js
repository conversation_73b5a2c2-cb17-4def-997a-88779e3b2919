Component({
  data: {
    selected: 0,
    color: '#A8A5D0',
    selectedColor: '#4B4EE8',
    list: [
      {
        pagePath: '/pages/home/<USER>',
        text: '首页',
        iconPath: '/assets/tabbar/home-regular.png',
        selectedIconPath: '/assets/tabbar/home-solid.png'
      },
      {
        pagePath: '/pages/ticket/index/index',
        text: '购票',
        iconPath: '/assets/tabbar/ticket-regular.png',
        selectedIconPath: '/assets/tabbar/ticket-solid.png'
      },
      {
        pagePath: '/pages/logs/logs',
        iconPath: '/assets/tabbar/qr-code.png',
        selectedIconPath: '/assets/tabbar/qr-code.png',
        isSpecial: true
      },
      {
        pagePath: '/pages/reservation/index/index',
        text: '预约',
        iconPath: '/assets/tabbar/check-regular.png',
        selectedIconPath: '/assets/tabbar/check-solid.png'
      },
      {
        pagePath: '/pages/user/index/index',
        text: '我的',
        iconPath: '/assets/tabbar/user-regular.png',
        selectedIconPath: '/assets/tabbar/user-solid.png'
      }
    ]
  },
  lifetimes: {
    attached() {
      this.updateSelected();
    }
  },
  methods: {
    updateSelected() {
      const pages = getCurrentPages();
      if (pages.length === 0) return;

      const currentPage = pages[pages.length - 1];
      const route = currentPage.route;

      let selected = 0;
      this.data.list.forEach((item, index) => {
        if (item.pagePath === `/${route}`) {
          selected = index;
        }
      });

      this.setData({ selected });
    },
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const index = parseInt(data.index);
      const path = data.path;

      // 特殊处理扫码按钮
      if (index === 2) {
        wx.scanCode({
          success: (res) => {
            console.log('扫码结果：', res);
          },
          fail: (err) => {
            console.log('扫码失败：', err);
          }
        });
        return;
      }

      // 更新选中状态
      this.setData({ selected: index });

      // 跳转页面
      wx.switchTab({
        url: path,
        fail: (err) => {
          console.error('跳转失败：', err);
        }
      });
    }
  }
});
