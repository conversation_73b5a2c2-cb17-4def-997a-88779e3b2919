Component({
  properties: {
    title: {
      type: String,
      value: ''
    },
    fg: {
      type: String,
      value: 'black'
    },
    bg: {
      type: String,
      value: 'transparent'
    }
  },

  data: {
    navBarHeight: 0,
    navBarTop: 0
  },

  lifetimes: {
    attached() {
      // 获取导航栏高度
      const menuBtn = wx.getMenuButtonBoundingClientRect();
      this.setData({
        navBarTop: menuBtn.top + 5,
        navBarHeight: menuBtn.height - 10
      });
    }
  },

  methods: {
    navigateBack() {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        wx.navigateBack();
      } else {
        wx.switchTab({
          url: '/pages/home/<USER>'
        });
      }
    }
  }
})
