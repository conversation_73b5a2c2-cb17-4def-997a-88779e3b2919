/**app.wxss**/

page {
  --paper: #f2f2f2;
  --silver: #eee;
  --bright: #ccc;
  --light: #999;
  --medium: #666;
  --dark: #333;
  --lightblue: #edecff;
  --primary: #4b4ee8;
  --secondary: #5c7cff;
  --tertiary: #8297f1;
  --softred: #ec536a;
  --shadow: rgba(0, 0, 0, 0.1);
  --translucence: rgba(0, 0, 0, 0.5);

  background-color: var(--paper);
}

/* 页面容器 */
.container {
  display: flex;
  width: 100%;
  min-height: 100vh;
  padding-bottom: 200rpx;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

.main-banner {
  background-color: var(--primary);
  width: 100%;
  box-sizing: border-box;
}

.main-card {
  position: relative;
  z-index: 10;
  background-color: white;
  margin-top: -60rpx;
  padding-top: 10rpx;
  border-radius: 60rpx 60rpx 0 0;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 -1rpx 5rpx var(--shadow);
}

.section {
  padding: 30rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  padding: 10rpx 0;
}

.content {
  font-size: 28rpx;
  color: var(--medium);
  margin-bottom: 50rpx;
  text-align: left;
  line-height: 1.8;
}

.btn {
  padding: 20rpx 40rpx;
  align-items: center;
  justify-content: center;
  background-color: var(--primary);
  color: white;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.4;
  border-radius: 100rpx;
  box-sizing: border-box;
}

.btn-sm {
  font-size: 0.8rem;
  padding: 16rpx 32rpx;
  line-height: 1;
}

/* 票务信息列表 */
.item-list {
  padding: 20rpx 0;
}

/* 票务列表项 */
.item {
  display: flex;
  background-color: white;
  width: 90vw;
  margin-bottom: 20rpx;
  padding: 20rpx;
  align-items: flex-start;
  border-radius: 15rpx;
  box-shadow: 0 0 10rpx var(--shadow);
}

/* 缩略图样式 */
.item-thumbnail {
  width: 180rpx;
  height: 240rpx;
  border-radius: 15rpx;
  margin-right: 25rpx;
}

.item-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
  width: 100%;
}

/* 标题样式 */
.item-title {
  display: -webkit-box;
  margin-bottom: 15rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: var(--dark);
  line-height: 1.8;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 单行简述 */
.item-desc {
  display: -webkit-box;
  margin: 5rpx 0;
  font-size: 24rpx;
  color: var(--medium);
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-spec {
  display: flex;
  width: 100%;
  margin-top: 15rpx;
  justify-content: flex-end;
}

.price {
  font-size: 24rpx;
  color: var(--primary);
  height: 100%;
  align-items: baseline;
}

.price .price-symbol {
  font-size: 28rpx;
  margin-right: 4rpx;
}

.price .price-value {
  font-size: 50rpx;
  font-weight: bold;
  margin-right: 4rpx;
}

.price .price-suffix {
  font-size: 24rpx;
  color: var(--light);
}

.empty {
  text-align: center;
  color: var(--light);
  font-size: 28rpx;
  padding: 100rpx 0;
}
